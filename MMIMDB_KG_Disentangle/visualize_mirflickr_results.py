"""
Visualization script for MIR-Flickr experiment results.
Generates comprehensive visualizations similar to MM-IMDB analysis.
"""

import os
import json
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd
from pathlib import Path
import argparse
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Set style
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

def load_results(results_path):
    """Load experiment results."""
    with open(results_path, 'r') as f:
        return json.load(f)

def plot_classification_metrics(results, output_dir):
    """Plot classification performance metrics."""
    logger.info("Generating classification metrics plots...")
    
    # Extract basic metrics
    metrics = results.get('basic_metrics', {})
    
    # Main metrics
    main_metrics = {
        'F1 Score': metrics.get('f1', 0),
        'Precision': metrics.get('precision', 0),
        'Recall': metrics.get('recall', 0),
        'mAP': metrics.get('mAP', 0)
    }
    
    # Create bar plot
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    # Bar plot
    bars = ax1.bar(main_metrics.keys(), main_metrics.values(), 
                   color=['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4'])
    ax1.set_title('MIR-Flickr Classification Performance', fontsize=14, fontweight='bold')
    ax1.set_ylabel('Score', fontsize=12)
    ax1.set_ylim(0, 1)
    
    # Add value labels on bars
    for bar, value in zip(bars, main_metrics.values()):
        ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                f'{value:.3f}', ha='center', va='bottom', fontweight='bold')
    
    # Radar plot
    angles = np.linspace(0, 2*np.pi, len(main_metrics), endpoint=False)
    values = list(main_metrics.values())
    values += values[:1]  # Complete the circle
    angles = np.concatenate((angles, [angles[0]]))
    
    ax2 = plt.subplot(122, projection='polar')
    ax2.plot(angles, values, 'o-', linewidth=2, color='#FF6B6B')
    ax2.fill(angles, values, alpha=0.25, color='#FF6B6B')
    ax2.set_xticks(angles[:-1])
    ax2.set_xticklabels(main_metrics.keys())
    ax2.set_ylim(0, 1)
    ax2.set_title('Performance Radar Chart', fontsize=14, fontweight='bold', pad=20)
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'mirflickr_classification_performance.png'), 
                dpi=300, bbox_inches='tight')
    plt.close()

def plot_per_concept_analysis(results, output_dir):
    """Plot per-concept performance analysis."""
    logger.info("Generating per-concept analysis...")
    
    metrics = results.get('basic_metrics', {})
    
    # MIR-Flickr concept names
    concept_names = [
        'animals', 'baby', 'bird', 'car', 'clouds', 'dog', 'female', 'flower', 'food', 'indoor',
        'lake', 'male', 'night', 'people', 'plant_life', 'portrait', 'river', 'sea', 'sky', 'structures',
        'sunset', 'transport', 'tree', 'water', 'cityscape', 'landscape', 'night_time', 'still_life',
        'family', 'group', 'natural', 'party', 'sport', 'travel', 'wedding', 'beach', 'mountain', 'urban'
    ]
    
    # Get per-class metrics
    per_class_precision = metrics.get('per_class_precision', [])
    per_class_recall = metrics.get('per_class_recall', [])
    per_class_f1 = metrics.get('per_class_f1', [])
    
    if not per_class_precision:
        logger.warning("No per-class metrics found, skipping per-concept analysis")
        return
    
    # Create DataFrame for easier plotting
    df = pd.DataFrame({
        'Concept': concept_names[:len(per_class_precision)],
        'Precision': per_class_precision,
        'Recall': per_class_recall,
        'F1': per_class_f1
    })
    
    # Sort by F1 score
    df = df.sort_values('F1', ascending=True)
    
    # Create horizontal bar plot
    fig, ax = plt.subplots(figsize=(12, 16))
    
    y_pos = np.arange(len(df))
    width = 0.25
    
    ax.barh(y_pos - width, df['Precision'], width, label='Precision', alpha=0.8)
    ax.barh(y_pos, df['Recall'], width, label='Recall', alpha=0.8)
    ax.barh(y_pos + width, df['F1'], width, label='F1', alpha=0.8)
    
    ax.set_yticks(y_pos)
    ax.set_yticklabels(df['Concept'])
    ax.set_xlabel('Score')
    ax.set_title('Per-Concept Performance (Sorted by F1 Score)', fontsize=14, fontweight='bold')
    ax.legend()
    ax.grid(axis='x', alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'mirflickr_per_concept_performance.png'), 
                dpi=300, bbox_inches='tight')
    plt.close()

def plot_disentanglement_analysis(results, output_dir):
    """Plot disentanglement analysis."""
    logger.info("Generating disentanglement analysis...")
    
    disentangle_metrics = results.get('disentanglement_metrics', {})
    
    if not disentangle_metrics:
        logger.warning("No disentanglement metrics found, skipping analysis")
        return
    
    # Extract disentanglement metrics
    metrics = {
        'Mutual Information': disentangle_metrics.get('mutual_information', 0),
        'Modularity': disentangle_metrics.get('modularity', 0),
        'Compactness': disentangle_metrics.get('compactness', 0),
        'Interpretability': disentangle_metrics.get('interpretability', 0),
        'Disentanglement Score': disentangle_metrics.get('disentanglement_score', 0)
    }
    
    # Create visualization
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    # Bar plot
    bars = ax1.bar(metrics.keys(), metrics.values(), 
                   color=['#FF9999', '#66B2FF', '#99FF99', '#FFCC99', '#FF99CC'])
    ax1.set_title('Disentanglement Metrics', fontsize=14, fontweight='bold')
    ax1.set_ylabel('Score', fontsize=12)
    ax1.set_ylim(0, 1)
    plt.setp(ax1.get_xticklabels(), rotation=45, ha='right')
    
    # Add value labels
    for bar, value in zip(bars, metrics.values()):
        ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                f'{value:.3f}', ha='center', va='bottom', fontweight='bold')
    
    # Radar plot
    angles = np.linspace(0, 2*np.pi, len(metrics), endpoint=False)
    values = list(metrics.values())
    values += values[:1]
    angles = np.concatenate((angles, [angles[0]]))
    
    ax2 = plt.subplot(122, projection='polar')
    ax2.plot(angles, values, 'o-', linewidth=2, color='#66B2FF')
    ax2.fill(angles, values, alpha=0.25, color='#66B2FF')
    ax2.set_xticks(angles[:-1])
    ax2.set_xticklabels(metrics.keys())
    ax2.set_ylim(0, 1)
    ax2.set_title('Disentanglement Radar Chart', fontsize=14, fontweight='bold', pad=20)
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'mirflickr_disentanglement_analysis.png'), 
                dpi=300, bbox_inches='tight')
    plt.close()

def plot_component_effectiveness(results, output_dir):
    """Plot component effectiveness analysis."""
    logger.info("Generating component effectiveness analysis...")
    
    comp_metrics = results.get('component_effectiveness', {})
    
    if not comp_metrics:
        logger.warning("No component effectiveness metrics found, skipping analysis")
        return
    
    # Extract component metrics
    components = {
        'Knowledge Graph': comp_metrics.get('kg_improvement', 0),
        'Adaptive Fusion': comp_metrics.get('fusion_effectiveness', 0),
        'Redundancy Detection': comp_metrics.get('redundancy_reduction', 0),
        'Graph Reasoning': comp_metrics.get('graph_reasoning_effect', 0)
    }
    
    # Create bar plot
    fig, ax = plt.subplots(figsize=(10, 6))
    
    bars = ax.bar(components.keys(), components.values(), 
                  color=['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4'])
    ax.set_title('Component Effectiveness Analysis', fontsize=14, fontweight='bold')
    ax.set_ylabel('Improvement Score', fontsize=12)
    plt.setp(ax.get_xticklabels(), rotation=45, ha='right')
    
    # Add value labels
    for bar, value in zip(bars, components.values()):
        ax.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.001,
                f'{value:.3f}', ha='center', va='bottom', fontweight='bold')
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'mirflickr_component_effectiveness.png'), 
                dpi=300, bbox_inches='tight')
    plt.close()

def plot_comparison_with_mmimdb(mirflickr_results, mmimdb_results, output_dir):
    """Plot comparison between MIR-Flickr and MM-IMDB results."""
    logger.info("Generating comparison with MM-IMDB...")
    
    # Extract metrics for comparison
    metrics = ['f1', 'precision', 'recall', 'mAP']
    
    mirflickr_values = [mirflickr_results.get('basic_metrics', {}).get(m, 0) for m in metrics]
    mmimdb_values = [mmimdb_results.get(m, 0) for m in metrics]
    
    # Create comparison plot
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    # Bar comparison
    x = np.arange(len(metrics))
    width = 0.35
    
    bars1 = ax1.bar(x - width/2, mmimdb_values, width, label='MM-IMDB', alpha=0.8)
    bars2 = ax1.bar(x + width/2, mirflickr_values, width, label='MIR-Flickr', alpha=0.8)
    
    ax1.set_xlabel('Metrics')
    ax1.set_ylabel('Score')
    ax1.set_title('Dataset Comparison: MM-IMDB vs MIR-Flickr', fontsize=14, fontweight='bold')
    ax1.set_xticks(x)
    ax1.set_xticklabels([m.upper() for m in metrics])
    ax1.legend()
    ax1.grid(axis='y', alpha=0.3)
    
    # Add value labels
    for bars in [bars1, bars2]:
        for bar in bars:
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                    f'{height:.3f}', ha='center', va='bottom', fontsize=9)
    
    # Difference plot
    differences = [m - mm for m, mm in zip(mirflickr_values, mmimdb_values)]
    colors = ['green' if d >= 0 else 'red' for d in differences]
    
    bars = ax2.bar(metrics, differences, color=colors, alpha=0.7)
    ax2.set_xlabel('Metrics')
    ax2.set_ylabel('Difference (MIR-Flickr - MM-IMDB)')
    ax2.set_title('Performance Difference', fontsize=14, fontweight='bold')
    ax2.axhline(y=0, color='black', linestyle='-', alpha=0.3)
    ax2.grid(axis='y', alpha=0.3)
    
    # Add value labels
    for bar, diff in zip(bars, differences):
        ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + (0.005 if diff >= 0 else -0.015),
                f'{diff:+.3f}', ha='center', va='bottom' if diff >= 0 else 'top', fontweight='bold')
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'mirflickr_mmimdb_comparison.png'), 
                dpi=300, bbox_inches='tight')
    plt.close()

def generate_summary_report(results, output_dir):
    """Generate a summary report."""
    logger.info("Generating summary report...")
    
    report_path = os.path.join(output_dir, 'mirflickr_results_summary.txt')
    
    with open(report_path, 'w') as f:
        f.write("MIR-Flickr Experiment Results Summary\n")
        f.write("="*50 + "\n\n")
        
        # Basic metrics
        basic_metrics = results.get('basic_metrics', {})
        f.write("Classification Performance:\n")
        f.write(f"  F1 Score: {basic_metrics.get('f1', 0):.4f}\n")
        f.write(f"  Precision: {basic_metrics.get('precision', 0):.4f}\n")
        f.write(f"  Recall: {basic_metrics.get('recall', 0):.4f}\n")
        f.write(f"  mAP: {basic_metrics.get('mAP', 0):.4f}\n\n")
        
        # Disentanglement metrics
        disentangle_metrics = results.get('disentanglement_metrics', {})
        if disentangle_metrics:
            f.write("Disentanglement Analysis:\n")
            f.write(f"  Mutual Information: {disentangle_metrics.get('mutual_information', 0):.4f}\n")
            f.write(f"  Modularity: {disentangle_metrics.get('modularity', 0):.4f}\n")
            f.write(f"  Compactness: {disentangle_metrics.get('compactness', 0):.4f}\n\n")
        
        # Component effectiveness
        comp_metrics = results.get('component_effectiveness', {})
        if comp_metrics:
            f.write("Component Effectiveness:\n")
            f.write(f"  KG Enhancement: {comp_metrics.get('kg_improvement', 0):.4f}\n")
            f.write(f"  Adaptive Fusion: {comp_metrics.get('fusion_effectiveness', 0):.4f}\n")
            f.write(f"  Redundancy Detection: {comp_metrics.get('redundancy_reduction', 0):.4f}\n\n")
        
        f.write("Generated visualizations:\n")
        f.write("  - mirflickr_classification_performance.png\n")
        f.write("  - mirflickr_per_concept_performance.png\n")
        f.write("  - mirflickr_disentanglement_analysis.png\n")
        f.write("  - mirflickr_component_effectiveness.png\n")
        f.write("  - mirflickr_mmimdb_comparison.png (if MM-IMDB results available)\n")
    
    logger.info(f"Summary report saved to: {report_path}")

def main():
    """Main visualization function."""
    parser = argparse.ArgumentParser(description="Visualize MIR-Flickr experiment results")
    
    parser.add_argument('--results_path', type=str, required=True,
                        help='Path to comprehensive results JSON file')
    parser.add_argument('--mmimdb_results_path', type=str, default=None,
                        help='Path to MM-IMDB results for comparison')
    parser.add_argument('--output_dir', type=str, default='./mirflickr_visualizations',
                        help='Output directory for visualizations')
    
    args = parser.parse_args()
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Load results
    logger.info(f"Loading MIR-Flickr results from: {args.results_path}")
    mirflickr_results = load_results(args.results_path)
    
    # Generate visualizations
    plot_classification_metrics(mirflickr_results, args.output_dir)
    plot_per_concept_analysis(mirflickr_results, args.output_dir)
    plot_disentanglement_analysis(mirflickr_results, args.output_dir)
    plot_component_effectiveness(mirflickr_results, args.output_dir)
    
    # Generate comparison if MM-IMDB results provided
    if args.mmimdb_results_path and os.path.exists(args.mmimdb_results_path):
        logger.info(f"Loading MM-IMDB results from: {args.mmimdb_results_path}")
        mmimdb_results = load_results(args.mmimdb_results_path)
        plot_comparison_with_mmimdb(mirflickr_results, mmimdb_results, args.output_dir)
    
    # Generate summary report
    generate_summary_report(mirflickr_results, args.output_dir)
    
    logger.info(f"All visualizations saved to: {args.output_dir}")

if __name__ == "__main__":
    main()
