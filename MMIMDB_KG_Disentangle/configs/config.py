"""
Configuration file for the MM-IMDB Knowledge Graph Disentanglement project.
"""

# Dataset configuration
DATASET_CONFIG = {
    'data_path': '/home/<USER>/workplace/dwb/data/imdb',
    'kg_path': '/home/<USER>/workplace/dwb/MMIMDB_KG_Disentangle/kg_data',
    'text_dim': 300,
    'visual_dim': 4096,
    'kg_dim': 200,
    'num_classes': 23,
    'genres': {
        'Drama': 0, 'Comedy': 1, 'Action': 2, 'Adventure': 3, 'Romance': 4,
        'Crime': 5, 'Horror': 6, 'Thriller': 7, 'Biography': 8, 'Animation': 9,
        'Family': 10, 'Mystery': 11, 'Fantasy': 12, 'Music': 13, 'History': 14,
        'Western': 15, 'Sci-Fi': 16, 'Musical': 17, 'Sport': 18, 'Short': 19,
        'War': 20, 'Documentary': 21, 'Film-Noir': 22
    }
}

# Model configuration
MODEL_CONFIG = {
    'hidden_dim': 512,
    'dropout': 0.3,
    'num_heads': 8,
    'temperature': 0.07,
    'redundancy_weight': 0.1,
    'contrastive_weight': 0.2,
    'kg_weight': 0.3
}

# Training configuration
TRAINING_CONFIG = {
    'batch_size': 32,
    'num_epochs': 30,
    'lr': 1e-4,
    'weight_decay': 1e-5,
    'patience': 5,
    'seed': 42
}

# Knowledge graph configuration
KG_CONFIG = {
    'relations': {
        'movie_director': 0,
        'movie_actor': 1,
        'movie_genre': 2,
        'director_actor': 3,
        'director_genre': 4,
        'actor_genre': 5,
        'co_occurrence': 6
    },
    'embedding_dim': 200
}

# Evaluation configuration
EVAL_CONFIG = {
    'threshold': 0.5,
    'metrics': ['precision', 'recall', 'f1', 'mAP']
}
