"""
Configuration file for the NUS-WIDE Knowledge Graph Disentanglement project.
"""

# Dataset configuration
NUSWIDE_DATASET_CONFIG = {
    'data_path': '/home/<USER>/workplace/dwb/data/nuswide',
    'kg_path': '/home/<USER>/workplace/dwb/MMIMDB_KG_Disentangle/kg_data',
    'text_dim': 300,
    'visual_dim': 4096,
    'kg_dim': 200,
    'num_classes': 81,
    'concepts': {
        'airport': 0, 'animal': 1, 'beach': 2, 'bear': 3, 'birds': 4, 
        'boats': 5, 'book': 6, 'bridge': 7, 'buildings': 8, 'cars': 9, 
        'castle': 10, 'cat': 11, 'cityscape': 12, 'clouds': 13, 'computer': 14, 
        'coral': 15, 'cow': 16, 'dancing': 17, 'dog': 18, 'earthquake': 19, 
        'elk': 20, 'fire': 21, 'fish': 22, 'flags': 23, 'flowers': 24, 
        'food': 25, 'fox': 26, 'frost': 27, 'garden': 28, 'glacier': 29, 
        'grass': 30, 'harbor': 31, 'horses': 32, 'house': 33, 'lake': 34, 
        'leaf': 35, 'map': 36, 'military': 37, 'moon': 38, 'mountain': 39, 
        'nighttime': 40, 'ocean': 41, 'person': 42, 'plane': 43, 'plants': 44, 
        'police': 45, 'protest': 46, 'railroad': 47, 'rainbow': 48, 'reflection': 49, 
        'road': 50, 'rocks': 51, 'running': 52, 'sand': 53, 'sign': 54, 
        'sky': 55, 'snow': 56, 'soccer': 57, 'sports': 58, 'statue': 59, 
        'street': 60, 'sun': 61, 'sunset': 62, 'surf': 63, 'swimmers': 64, 
        'tattoo': 65, 'temple': 66, 'tiger': 67, 'tower': 68, 'town': 69, 
        'toy': 70, 'train': 71, 'tree': 72, 'valley': 73, 'vehicle': 74, 
        'water': 75, 'waterfall': 76, 'wedding': 77, 'whales': 78, 'window': 79, 
        'zebra': 80
    }
}

# Model configuration (same as MM-IMDB)
NUSWIDE_MODEL_CONFIG = {
    'hidden_dim': 512,
    'dropout': 0.3,
    'num_heads': 8,
    'temperature': 0.07,
    'redundancy_weight': 0.1,
    'contrastive_weight': 0.2,
    'kg_weight': 0.3
}

# Training configuration (same as MM-IMDB)
NUSWIDE_TRAINING_CONFIG = {
    'batch_size': 32,
    'num_epochs': 30,
    'lr': 1e-4,
    'weight_decay': 1e-5,
    'patience': 5,
    'seed': 42
}

# Knowledge graph configuration
NUSWIDE_KG_CONFIG = {
    'relations': {
        'image_tag': 0,
        'image_concept': 1,
        'tag_concept': 2,
        'co_occurrence': 3
    },
    'embedding_dim': 200
}

# Evaluation configuration (same as MM-IMDB)
NUSWIDE_EVAL_CONFIG = {
    'threshold': 0.5,
    'metrics': ['precision', 'recall', 'f1', 'mAP']
}
