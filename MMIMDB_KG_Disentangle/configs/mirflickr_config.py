"""
Configuration file for the MIR-Flickr Knowledge Graph Disentanglement project.
"""

# Dataset configuration
MIRFLICKR_DATASET_CONFIG = {
    'data_path': '/home/<USER>/workplace/dwb/data/mirflickr',
    'kg_path': '/home/<USER>/workplace/dwb/MMIMDB_KG_Disentangle/kg_data',
    'text_dim': 300,
    'visual_dim': 4096,
    'kg_dim': 200,
    'num_classes': 38,
    'concepts': {
        'animals': 0, 'baby': 1, 'bird': 2, 'car': 3, 'clouds': 4, 
        'dog': 5, 'female': 6, 'flower': 7, 'food': 8, 'indoor': 9, 
        'lake': 10, 'male': 11, 'night': 12, 'people': 13, 'plant_life': 14, 
        'portrait': 15, 'river': 16, 'sea': 17, 'sky': 18, 'structures': 19, 
        'sunset': 20, 'transport': 21, 'tree': 22, 'water': 23, 'cityscape': 24, 
        'landscape': 25, 'night_time': 26, 'still_life': 27, 'family': 28, 
        'group': 29, 'natural': 30, 'party': 31, 'sport': 32, 'travel': 33, 
        'wedding': 34, 'beach': 35, 'mountain': 36, 'urban': 37
    }
}

# Model configuration (same as MM-IMDB)
MIRFLICKR_MODEL_CONFIG = {
    'hidden_dim': 512,
    'dropout': 0.3,
    'num_heads': 8,
    'temperature': 0.07,
    'redundancy_weight': 0.1,
    'contrastive_weight': 0.2,
    'kg_weight': 0.3
}

# Training configuration (same as MM-IMDB)
MIRFLICKR_TRAINING_CONFIG = {
    'batch_size': 32,
    'num_epochs': 30,
    'lr': 1e-4,
    'weight_decay': 1e-5,
    'patience': 5,
    'seed': 42
}

# Knowledge graph configuration
MIRFLICKR_KG_CONFIG = {
    'relations': {
        'image_tag': 0,
        'image_concept': 1,
        'tag_concept': 2,
        'co_occurrence': 3
    },
    'embedding_dim': 200
}

# Evaluation configuration (same as MM-IMDB)
MIRFLICKR_EVAL_CONFIG = {
    'threshold': 0.5,
    'metrics': ['precision', 'recall', 'f1', 'mAP']
}
