"""
Complete experiment script for MIR-Flickr dataset with KG-Disentangle-Net.
This script implements the same complete pipeline as MM-IMDB including:
1. Data preparation and knowledge graph construction
2. Training, validation, and testing
3. Comprehensive evaluation and analysis
4. Visualization and result comparison
"""

import os
import sys
import argparse
import torch
import logging
import json
import numpy as np
from datetime import datetime
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.append(str(project_root))

from train_mirflickr import train_mirflickr
from utils.mirflickr_kg_constructor import MIR<PERSON>lickrKnowledgeGraphConstructor
from configs.mirflickr_config import MIRFLICKR_DATASET_CONFIG, MIRFLICKR_MODEL_CONFIG, MIRFLICKR_TRAINING_CONFIG

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(levelname)s - %(name)s - %(message)s',
    datefmt='%m/%d/%Y %H:%M:%S',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

def setup_experiment_directory(base_output_dir, exp_name):
    """Setup experiment directory structure."""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    exp_dir = os.path.join(base_output_dir, f"mirflickr_complete_experiment_{timestamp}")

    # Create subdirectories
    subdirs = [
        'models',
        'logs',
        'results',
        'visualizations',
        'analysis',
        'comparison'
    ]

    for subdir in subdirs:
        os.makedirs(os.path.join(exp_dir, subdir), exist_ok=True)

    logger.info(f"Experiment directory created: {exp_dir}")
    return exp_dir

def prepare_data(args):
    """Prepare MIR-Flickr dataset."""
    logger.info("=== Step 1: Data Preparation ===")

    # Check if data already exists
    if os.path.exists(os.path.join(args.data_path, 'images')) and \
       len(os.listdir(os.path.join(args.data_path, 'images'))) > 100:
        logger.info("MIR-Flickr data already exists, skipping preparation")
        return True

    # Prepare data using existing script
    try:
        from prepare_mirflickr import main as prepare_main
        import sys

        # Temporarily modify sys.argv for prepare_mirflickr
        original_argv = sys.argv
        sys.argv = ['prepare_mirflickr.py', '--output_dir', args.data_path]

        if args.mock_data:
            sys.argv.extend(['--mock', '--num_mock_samples', str(args.num_mock_samples)])

        prepare_main()
        sys.argv = original_argv

        logger.info("Data preparation completed successfully")
        return True

    except Exception as e:
        logger.error(f"Data preparation failed: {e}")
        return False

def build_knowledge_graph(args):
    """Build knowledge graph for MIR-Flickr."""
    logger.info("=== Step 2: Knowledge Graph Construction ===")

    # Check if KG already exists
    kg_file = os.path.join(args.kg_path, 'mirflickr_knowledge_graph.pkl')
    if os.path.exists(kg_file):
        logger.info("Knowledge graph already exists, skipping construction")
        return True

    try:
        # Build knowledge graph
        kg_constructor = MIRFlickrKnowledgeGraphConstructor(
            data_path=args.data_path,
            output_path=args.kg_path
        )
        kg_constructor.construct()

        logger.info("Knowledge graph construction completed successfully")
        return True

    except Exception as e:
        logger.error(f"Knowledge graph construction failed: {e}")
        return False

def train_model(args, exp_dir):
    """Train the KG-Disentangle-Net model on MIR-Flickr."""
    logger.info("=== Step 3: Model Training ===")

    try:
        # Setup training arguments
        train_args = argparse.Namespace()

        # Copy arguments
        for key, value in vars(args).items():
            setattr(train_args, key, value)

        # Set specific training parameters
        train_args.output_dir = os.path.join(exp_dir, 'models')
        train_args.exp_name = f"mirflickr_kg_disentangle_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        train_args.notes = "Complete MIR-Flickr experiment with KG-Disentangle-Net"

        # Set default values for missing parameters
        if not hasattr(train_args, 'sample_ratio'):
            train_args.sample_ratio = 1.0
        if not hasattr(train_args, 'seed'):
            train_args.seed = 42
        if not hasattr(train_args, 'weight_decay'):
            train_args.weight_decay = 1e-5

        # Train the model
        best_model_path, test_results = train_mirflickr(train_args)

        # Save training results
        results_file = os.path.join(exp_dir, 'results', 'training_results.json')
        with open(results_file, 'w') as f:
            json.dump(test_results, f, indent=2)

        logger.info(f"Model training completed successfully")
        logger.info(f"Best model saved at: {best_model_path}")
        logger.info(f"Test results: F1={test_results.get('f1', 0):.4f}, mAP={test_results.get('mAP', 0):.4f}")

        return best_model_path, test_results

    except Exception as e:
        logger.error(f"Model training failed: {e}")
        return None, None

def evaluate_model(args, exp_dir, model_path, test_results):
    """Comprehensive model evaluation."""
    logger.info("=== Step 4: Comprehensive Evaluation ===")

    try:
        # 1. Enhanced evaluation with disentanglement metrics
        logger.info("Computing disentanglement metrics...")
        try:
            from enhanced_evaluate import compute_disentanglement_metrics
            disentanglement_results = compute_disentanglement_metrics(
                model_path=model_path,
                data_path=args.data_path,
                kg_path=args.kg_path,
                output_dir=os.path.join(exp_dir, 'analysis'),
                dataset_type='mirflickr'
            )
        except ImportError:
            logger.warning("Enhanced evaluation not available, using basic metrics")
            disentanglement_results = {}

        # 2. Component effectiveness analysis
        logger.info("Analyzing component effectiveness...")
        try:
            from evaluate_component_effectiveness import evaluate_component_effectiveness
            component_results = evaluate_component_effectiveness(
                model_path=model_path,
                data_path=args.data_path,
                kg_path=args.kg_path,
                output_dir=os.path.join(exp_dir, 'analysis')
            )
        except ImportError:
            logger.warning("Component effectiveness analysis not available")
            component_results = {}

        # 3. KG disentanglement evaluation
        logger.info("Evaluating KG disentanglement...")
        try:
            from kg_disentanglement_metrics import evaluate_kg_disentanglement
            kg_results = evaluate_kg_disentanglement(
                model_path=model_path,
                data_path=args.data_path,
                kg_path=args.kg_path,
                output_dir=os.path.join(exp_dir, 'analysis'),
                dataset_type='mirflickr'
            )
        except ImportError:
            logger.warning("KG disentanglement evaluation not available")
            kg_results = {}

        # Combine all results
        comprehensive_results = {
            'basic_metrics': test_results,
            'disentanglement_metrics': disentanglement_results,
            'component_effectiveness': component_results,
            'kg_disentanglement': kg_results,
            'experiment_info': {
                'dataset': 'MIR-Flickr',
                'model': 'KG-Disentangle-Net',
                'timestamp': datetime.now().isoformat(),
                'data_path': args.data_path,
                'kg_path': args.kg_path
            }
        }

        # Save comprehensive results
        results_file = os.path.join(exp_dir, 'results', 'comprehensive_results.json')
        with open(results_file, 'w') as f:
            json.dump(comprehensive_results, f, indent=2)

        logger.info("Comprehensive evaluation completed successfully")
        return comprehensive_results

    except Exception as e:
        logger.error(f"Model evaluation failed: {e}")
        return None

def generate_visualizations(args, exp_dir, comprehensive_results):
    """Generate visualizations and analysis plots."""
    logger.info("=== Step 5: Visualization Generation ===")

    try:
        vis_dir = os.path.join(exp_dir, 'visualizations')

        # 1. Generate feature visualizations
        logger.info("Generating feature visualizations...")
        from visualize_features import generate_feature_visualizations
        generate_feature_visualizations(
            model_path=os.path.join(exp_dir, 'models'),
            data_path=args.data_path,
            kg_path=args.kg_path,
            output_dir=vis_dir,
            dataset_type='mirflickr'
        )

        # 2. Generate result visualizations
        logger.info("Generating result visualizations...")
        from visualize_results import generate_result_visualizations
        generate_result_visualizations(
            results=comprehensive_results,
            output_dir=vis_dir,
            dataset_type='mirflickr'
        )

        # 3. Generate knowledge graph visualization
        logger.info("Generating knowledge graph visualization...")
        from visualize_knowledge_graph import visualize_mirflickr_kg
        visualize_mirflickr_kg(
            kg_path=args.kg_path,
            output_dir=vis_dir
        )

        logger.info("Visualization generation completed successfully")
        return True

    except Exception as e:
        logger.warning(f"Visualization generation failed: {e}")
        return False

def compare_with_mmimdb(args, exp_dir, comprehensive_results):
    """Compare results with MM-IMDB baseline."""
    logger.info("=== Step 6: Comparison with MM-IMDB ===")

    try:
        comparison_dir = os.path.join(exp_dir, 'comparison')

        # Find MM-IMDB results
        mmimdb_results_path = None
        mmimdb_output_dir = os.path.join(project_root, 'output')

        # Look for the most recent MM-IMDB results
        if os.path.exists(mmimdb_output_dir):
            for subdir in os.listdir(mmimdb_output_dir):
                test_results_file = os.path.join(mmimdb_output_dir, subdir, 'test_results.json')
                if os.path.exists(test_results_file):
                    mmimdb_results_path = test_results_file
                    break

        if mmimdb_results_path:
            logger.info(f"Found MM-IMDB results at: {mmimdb_results_path}")

            # Load MM-IMDB results
            with open(mmimdb_results_path, 'r') as f:
                mmimdb_results = json.load(f)

            # Generate comparison
            from compare_mirflickr_results import generate_comparison
            comparison_results = generate_comparison(
                mmimdb_results=mmimdb_results,
                mirflickr_results=comprehensive_results['basic_metrics'],
                output_dir=comparison_dir
            )

            # Save comparison results
            comparison_file = os.path.join(comparison_dir, 'dataset_comparison.json')
            with open(comparison_file, 'w') as f:
                json.dump(comparison_results, f, indent=2)

            logger.info("Comparison with MM-IMDB completed successfully")
            return comparison_results
        else:
            logger.warning("No MM-IMDB results found for comparison")
            return None

    except Exception as e:
        logger.warning(f"Comparison with MM-IMDB failed: {e}")
        return None

def generate_final_report(args, exp_dir, comprehensive_results, comparison_results=None):
    """Generate final experiment report."""
    logger.info("=== Step 7: Final Report Generation ===")

    try:
        report_file = os.path.join(exp_dir, 'EXPERIMENT_REPORT.md')

        with open(report_file, 'w') as f:
            f.write("# MIR-Flickr KG-Disentangle-Net Experiment Report\n\n")
            f.write(f"**Experiment Date:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"**Dataset:** MIR-Flickr\n")
            f.write(f"**Model:** Knowledge Graph Enhanced Cross-Modal Semantic Disentanglement Network\n\n")

            # Basic metrics
            f.write("## Classification Performance\n\n")
            basic_metrics = comprehensive_results['basic_metrics']
            f.write(f"- **F1 Score:** {basic_metrics.get('f1', 0):.4f}\n")
            f.write(f"- **Precision:** {basic_metrics.get('precision', 0):.4f}\n")
            f.write(f"- **Recall:** {basic_metrics.get('recall', 0):.4f}\n")
            f.write(f"- **mAP:** {basic_metrics.get('mAP', 0):.4f}\n\n")

            # Disentanglement metrics
            if 'disentanglement_metrics' in comprehensive_results:
                f.write("## Disentanglement Analysis\n\n")
                disentangle_metrics = comprehensive_results['disentanglement_metrics']
                f.write(f"- **Mutual Information:** {disentangle_metrics.get('mutual_information', 0):.4f}\n")
                f.write(f"- **Modularity:** {disentangle_metrics.get('modularity', 0):.4f}\n")
                f.write(f"- **Compactness:** {disentangle_metrics.get('compactness', 0):.4f}\n\n")

            # Component effectiveness
            if 'component_effectiveness' in comprehensive_results:
                f.write("## Component Effectiveness\n\n")
                comp_metrics = comprehensive_results['component_effectiveness']
                f.write(f"- **KG Enhancement:** {comp_metrics.get('kg_improvement', 0):.4f}\n")
                f.write(f"- **Adaptive Fusion:** {comp_metrics.get('fusion_effectiveness', 0):.4f}\n")
                f.write(f"- **Redundancy Detection:** {comp_metrics.get('redundancy_reduction', 0):.4f}\n\n")

            # Comparison with MM-IMDB
            if comparison_results:
                f.write("## Comparison with MM-IMDB\n\n")
                f.write("| Metric | MM-IMDB | MIR-Flickr | Difference |\n")
                f.write("|--------|---------|------------|------------|\n")
                for metric in ['f1', 'precision', 'recall', 'mAP']:
                    mmimdb_val = comparison_results.get('mmimdb', {}).get(metric, 0)
                    mirflickr_val = comparison_results.get('mirflickr', {}).get(metric, 0)
                    diff = mirflickr_val - mmimdb_val
                    f.write(f"| {metric.upper()} | {mmimdb_val:.4f} | {mirflickr_val:.4f} | {diff:+.4f} |\n")
                f.write("\n")

            # Experiment configuration
            f.write("## Experiment Configuration\n\n")
            f.write(f"- **Data Path:** {args.data_path}\n")
            f.write(f"- **KG Path:** {args.kg_path}\n")
            f.write(f"- **Batch Size:** {args.batch_size}\n")
            f.write(f"- **Learning Rate:** {args.lr}\n")
            f.write(f"- **Epochs:** {args.num_epochs}\n")
            f.write(f"- **Device:** {args.device}\n\n")

            # Files and outputs
            f.write("## Generated Files\n\n")
            f.write("- `results/training_results.json` - Basic training and test results\n")
            f.write("- `results/comprehensive_results.json` - Complete evaluation results\n")
            f.write("- `analysis/` - Detailed analysis files\n")
            f.write("- `visualizations/` - Generated plots and visualizations\n")
            f.write("- `comparison/` - Comparison with MM-IMDB results\n")
            f.write("- `models/` - Trained model files\n\n")

            f.write("## Conclusion\n\n")
            f.write("This experiment demonstrates the effectiveness of the KG-Disentangle-Net ")
            f.write("approach on the MIR-Flickr dataset, showing its generalization capability ")
            f.write("across different domains (movie data vs. general web images).\n")

        logger.info(f"Final report generated: {report_file}")
        return report_file

    except Exception as e:
        logger.error(f"Final report generation failed: {e}")
        return None

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Complete MIR-Flickr experiment with KG-Disentangle-Net")

    # Data arguments
    parser.add_argument('--data_path', type=str, default='/home/<USER>/workplace/dwb/data/mirflickr',
                        help='Path to MIR-Flickr dataset')
    parser.add_argument('--kg_path', type=str, default='/home/<USER>/workplace/dwb/MMIMDB_KG_Disentangle/kg_data',
                        help='Path to knowledge graph data')
    parser.add_argument('--output_dir', type=str, default='./output_mirflickr_complete',
                        help='Output directory for experiment results')

    # Data preparation arguments
    parser.add_argument('--mock_data', action='store_true',
                        help='Use mock data instead of downloading real dataset')
    parser.add_argument('--num_mock_samples', type=int, default=1000,
                        help='Number of mock samples to create')

    # Training arguments
    parser.add_argument('--batch_size', type=int, default=32,
                        help='Batch size for training')
    parser.add_argument('--num_epochs', type=int, default=30,
                        help='Number of training epochs')
    parser.add_argument('--lr', type=float, default=1e-4,
                        help='Learning rate')
    parser.add_argument('--weight_decay', type=float, default=1e-5,
                        help='Weight decay')
    parser.add_argument('--seed', type=int, default=42,
                        help='Random seed')
    parser.add_argument('--device', type=str, default='cuda',
                        help='Device to use for training')

    # Experiment arguments
    parser.add_argument('--exp_name', type=str, default='mirflickr_complete_experiment',
                        help='Experiment name')
    parser.add_argument('--skip_training', action='store_true',
                        help='Skip training and use existing model')
    parser.add_argument('--skip_visualization', action='store_true',
                        help='Skip visualization generation')
    parser.add_argument('--skip_comparison', action='store_true',
                        help='Skip comparison with MM-IMDB')

    return parser.parse_args()

def main():
    """Main experiment function."""
    args = parse_args()

    logger.info("="*80)
    logger.info("MIR-Flickr Complete Experiment with KG-Disentangle-Net")
    logger.info("="*80)

    # Setup experiment directory
    exp_dir = setup_experiment_directory(args.output_dir, args.exp_name)

    # Save experiment configuration
    config_file = os.path.join(exp_dir, 'experiment_config.json')
    with open(config_file, 'w') as f:
        json.dump(vars(args), f, indent=2)

    try:
        # Step 1: Data preparation
        if not prepare_data(args):
            logger.error("Data preparation failed. Exiting.")
            return

        # Step 2: Knowledge graph construction
        if not build_knowledge_graph(args):
            logger.error("Knowledge graph construction failed. Exiting.")
            return

        # Step 3: Model training
        if not args.skip_training:
            model_path, test_results = train_model(args, exp_dir)
            if model_path is None:
                logger.error("Model training failed. Exiting.")
                return
        else:
            # Look for existing model
            model_path = None
            models_dir = os.path.join(exp_dir, 'models')
            if os.path.exists(models_dir):
                for file in os.listdir(models_dir):
                    if file.endswith('.pth'):
                        model_path = os.path.join(models_dir, file)
                        break

            if model_path is None:
                logger.error("No existing model found and training skipped. Exiting.")
                return

            # Load existing test results
            test_results_file = os.path.join(exp_dir, 'results', 'training_results.json')
            if os.path.exists(test_results_file):
                with open(test_results_file, 'r') as f:
                    test_results = json.load(f)
            else:
                test_results = {}

        # Step 4: Comprehensive evaluation
        comprehensive_results = evaluate_model(args, exp_dir, model_path, test_results)
        if comprehensive_results is None:
            logger.error("Model evaluation failed. Exiting.")
            return

        # Step 5: Visualization generation
        if not args.skip_visualization:
            generate_visualizations(args, exp_dir, comprehensive_results)

        # Step 6: Comparison with MM-IMDB
        comparison_results = None
        if not args.skip_comparison:
            comparison_results = compare_with_mmimdb(args, exp_dir, comprehensive_results)

        # Step 7: Final report generation
        report_file = generate_final_report(args, exp_dir, comprehensive_results, comparison_results)

        # Summary
        logger.info("="*80)
        logger.info("EXPERIMENT COMPLETED SUCCESSFULLY")
        logger.info("="*80)
        logger.info(f"Experiment directory: {exp_dir}")
        logger.info(f"Final report: {report_file}")

        # Print key results
        basic_metrics = comprehensive_results['basic_metrics']
        logger.info(f"Key Results:")
        logger.info(f"  F1 Score: {basic_metrics.get('f1', 0):.4f}")
        logger.info(f"  Precision: {basic_metrics.get('precision', 0):.4f}")
        logger.info(f"  Recall: {basic_metrics.get('recall', 0):.4f}")
        logger.info(f"  mAP: {basic_metrics.get('mAP', 0):.4f}")

        if comparison_results:
            logger.info(f"Comparison with MM-IMDB:")
            for metric in ['f1', 'precision', 'recall', 'mAP']:
                mmimdb_val = comparison_results.get('mmimdb', {}).get(metric, 0)
                mirflickr_val = comparison_results.get('mirflickr', {}).get(metric, 0)
                diff = mirflickr_val - mmimdb_val
                logger.info(f"  {metric.upper()}: MM-IMDB={mmimdb_val:.4f}, MIR-Flickr={mirflickr_val:.4f}, Diff={diff:+.4f}")

        logger.info("="*80)

    except Exception as e:
        logger.error(f"Experiment failed with error: {e}")
        raise

if __name__ == "__main__":
    main()
