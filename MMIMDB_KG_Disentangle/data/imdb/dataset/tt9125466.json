{"title": "Dakota", "director": "<PERSON>", "plot": "Dakota: Directed by <PERSON>. With <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>. A talented young singer gives up on her dreams to pursue a more stable life, but when that life is blown apart due to excess drinking and toxic relationships, she decides to piece those dreams back together using the one tool she has: her music.", "genres": ["Comedy", "Drama", "Music"], "cast": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "poster_src": "https://m.media-amazon.com/images/M/MV5BMDliMWU2YjQtNDBkZC00YjBlLWI5ODEtMGVlNDI0MDU2MzQ5XkEyXkFqcGdeQXVyOTUyMDE5MTk@._V1_QL75_UX190_CR0,0,190,281_.jpg"}