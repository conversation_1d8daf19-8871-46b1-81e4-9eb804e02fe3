{"title": "Magnolia", "director": "<PERSON>", "plot": "Magnolia: Directed by <PERSON>. With <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>. When an oil pipeline/rig worker, <PERSON>, returns home to the shores of Mississippi, he finds himself falling for <PERSON>; a beautiful, edgy mechanic from the gulf coast. But his homecoming has resurrected painful memories, and as he considers his future, he must confront his past. Thematically based on <PERSON>&#x27;s upcoming album <PERSON><PERSON>, Noah&#x27;s story takes him on a journey through love, heartache, and the unknown. Magnolia is based on the songs, lyrics, and life stories collected from <PERSON> over his career. Both a narrative film and a visual album Ma<PERSON> presents a story as seen and heard through <PERSON>&#x27;s bluesy southern roots.", "genres": ["Drama", "Music"], "cast": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Princess <PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "poster_src": "https://m.media-amazon.com/images/M/MV5BNGFjMTNkZDAtMjZiYy00NDBjLWI5NDYtNjBiMDQ4NmFmZTljXkEyXkFqcGdeQXVyNjY2NzM5OTk@._V1_QL75_UY281_CR11,0,190,281_.jpg"}