"""
Quick test script for MIR-Flickr experiment.
This script runs a simplified version of the complete experiment for testing purposes.
"""

import os
import sys
import argparse
import torch
import logging
import json
import numpy as np
from datetime import datetime
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.append(str(project_root))

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(levelname)s - %(name)s - %(message)s',
    datefmt='%m/%d/%Y %H:%M:%S',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

def create_mock_data(data_path, num_samples=100):
    """Create minimal mock data for testing."""
    logger.info(f"Creating mock data at {data_path} with {num_samples} samples...")
    
    # Create directories
    os.makedirs(os.path.join(data_path, 'images'), exist_ok=True)
    os.makedirs(os.path.join(data_path, 'tags'), exist_ok=True)
    os.makedirs(os.path.join(data_path, 'annotations'), exist_ok=True)
    
    # Create mock images (just empty files for testing)
    for i in range(1, num_samples + 1):
        img_path = os.path.join(data_path, 'images', f'im{i}.jpg')
        if not os.path.exists(img_path):
            # Create a minimal JPEG file
            with open(img_path, 'wb') as f:
                # Minimal JPEG header
                f.write(b'\xff\xd8\xff\xe0\x00\x10JFIF\x00\x01\x01\x01\x00H\x00H\x00\x00\xff\xdb\x00C\x00')
                f.write(b'\x08\x06\x06\x07\x06\x05\x08\x07\x07\x07\t\t\x08\n\x0c\x14\r\x0c\x0b\x0b\x0c\x19\x12\x13\x0f')
                f.write(b'\x14\x1d\x1a\x1f\x1e\x1d\x1a\x1c\x1c $.\' ",#\x1c\x1c(7),01444\x1f\'9=82<.342\xff\xc0\x00\x11\x08\x00\x01\x00\x01\x01\x01\x11\x00\x02\x11\x01\x03\x11\x01\xff\xc4\x00\x14\x00\x01\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x08\xff\xc4\x00\x14\x10\x01\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\xff\xda\x00\x0c\x03\x01\x00\x02\x11\x03\x11\x00\x3f\x00\xaa\xff\xd9')
        
        # Create mock tags
        tags_path = os.path.join(data_path, 'tags', f'im{i}.txt')
        if not os.path.exists(tags_path):
            with open(tags_path, 'w') as f:
                # Random tags
                tags = [f'tag{j}' for j in range(1, np.random.randint(3, 8))]
                f.write(' '.join(tags))
    
    # Create mock annotations (38 concepts)
    for concept_id in range(1, 39):
        ann_path = os.path.join(data_path, 'annotations', f'annotation_{concept_id}.txt')
        if not os.path.exists(ann_path):
            with open(ann_path, 'w') as f:
                # Random binary annotations
                annotations = [str(np.random.randint(0, 2)) for _ in range(num_samples)]
                f.write('\n'.join(annotations))
    
    logger.info(f"Mock data created successfully at {data_path}")

def create_mock_kg(kg_path):
    """Create minimal mock knowledge graph."""
    logger.info(f"Creating mock knowledge graph at {kg_path}...")
    
    os.makedirs(kg_path, exist_ok=True)
    
    # Create entity2id file
    entity_file = os.path.join(kg_path, 'mirflickr_entity2id.txt')
    if not os.path.exists(entity_file):
        with open(entity_file, 'w') as f:
            f.write("entity\tid\n")
            # Add some mock entities
            for i in range(100):
                f.write(f"entity_{i}\t{i}\n")
    
    # Create relation2id file
    relation_file = os.path.join(kg_path, 'mirflickr_relation2id.txt')
    if not os.path.exists(relation_file):
        with open(relation_file, 'w') as f:
            f.write("relation\tid\n")
            f.write("image_tag\t0\n")
            f.write("image_concept\t1\n")
            f.write("tag_concept\t2\n")
            f.write("co_occurrence\t3\n")
    
    # Create triple file
    triple_file = os.path.join(kg_path, 'mirflickr_triple.txt')
    if not os.path.exists(triple_file):
        with open(triple_file, 'w') as f:
            # Generate some mock triples
            for i in range(500):
                head = np.random.randint(0, 100)
                relation = np.random.randint(0, 4)
                tail = np.random.randint(0, 100)
                f.write(f"{head}\t{relation}\t{tail}\n")
    
    # Create mock knowledge graph pickle
    import pickle
    kg_pickle = os.path.join(kg_path, 'mirflickr_knowledge_graph.pkl')
    if not os.path.exists(kg_pickle):
        kg_data = {
            'entities': {f'entity_{i}': i for i in range(100)},
            'relations': {'image_tag': 0, 'image_concept': 1, 'tag_concept': 2, 'co_occurrence': 3},
            'triples': [(i, np.random.randint(0, 4), np.random.randint(0, 100)) for i in range(500)]
        }
        with open(kg_pickle, 'wb') as f:
            pickle.dump(kg_data, f)
    
    logger.info(f"Mock knowledge graph created at {kg_path}")

def run_quick_training(args):
    """Run quick training with minimal epochs."""
    logger.info("Starting quick training...")
    
    try:
        from train_mirflickr import train_mirflickr
        
        # Setup training arguments
        train_args = argparse.Namespace()
        
        # Copy arguments
        for key, value in vars(args).items():
            setattr(train_args, key, value)
        
        # Override for quick testing
        train_args.num_epochs = 2  # Very short training
        train_args.batch_size = 8  # Small batch size
        train_args.exp_name = f"mirflickr_quick_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        train_args.notes = "Quick test run for MIR-Flickr"
        
        # Train the model
        best_model_path, test_results = train_mirflickr(train_args)
        
        logger.info(f"Quick training completed")
        logger.info(f"Test results: {test_results}")
        
        return best_model_path, test_results
        
    except Exception as e:
        logger.error(f"Quick training failed: {e}")
        return None, None

def run_quick_evaluation(model_path, args):
    """Run quick evaluation."""
    logger.info("Starting quick evaluation...")
    
    try:
        from evaluate_mirflickr import load_model, evaluate_model
        from utils.mirflickr_dataset import MIRFlickrDataset
        from torch.utils.data import DataLoader
        
        # Set device
        device = torch.device(args.device if torch.cuda.is_available() else 'cpu')
        
        # Load model
        model = load_model(model_path, device)
        
        # Create test dataset
        test_dataset = MIRFlickrDataset(
            data_path=args.data_path,
            kg_path=args.kg_path,
            mode='test'
        )
        
        # Create data loader
        test_loader = DataLoader(
            test_dataset,
            batch_size=args.batch_size,
            shuffle=False,
            num_workers=0,  # No multiprocessing for quick test
            pin_memory=False
        )
        
        # Evaluate
        eval_args = argparse.Namespace()
        eval_args.split = 'test'
        eval_args.threshold = 0.5
        eval_args.output_dir = args.output_dir
        
        metrics, preds, labels, redundancy_scores = evaluate_model(model, test_loader, device, eval_args)
        
        logger.info(f"Quick evaluation completed")
        logger.info(f"Evaluation metrics: {metrics}")
        
        return metrics
        
    except Exception as e:
        logger.error(f"Quick evaluation failed: {e}")
        return None

def main():
    """Main quick test function."""
    parser = argparse.ArgumentParser(description="Quick test for MIR-Flickr experiment")
    
    # Data arguments
    parser.add_argument('--data_path', type=str, default='/tmp/mirflickr_test',
                        help='Path to test data directory')
    parser.add_argument('--kg_path', type=str, default='/tmp/mirflickr_kg_test',
                        help='Path to test KG directory')
    parser.add_argument('--output_dir', type=str, default='./output_mirflickr_test',
                        help='Output directory for test results')
    
    # Test arguments
    parser.add_argument('--num_samples', type=int, default=50,
                        help='Number of mock samples to create')
    parser.add_argument('--batch_size', type=int, default=8,
                        help='Batch size for testing')
    parser.add_argument('--device', type=str, default='cpu',
                        help='Device to use for testing')
    parser.add_argument('--skip_training', action='store_true',
                        help='Skip training and only test data loading')
    
    args = parser.parse_args()
    
    logger.info("="*60)
    logger.info("MIR-Flickr Quick Test")
    logger.info("="*60)
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    try:
        # Step 1: Create mock data
        create_mock_data(args.data_path, args.num_samples)
        
        # Step 2: Create mock knowledge graph
        create_mock_kg(args.kg_path)
        
        # Step 3: Test data loading
        logger.info("Testing data loading...")
        from utils.mirflickr_dataset import MIRFlickrDataset
        
        test_dataset = MIRFlickrDataset(
            data_path=args.data_path,
            kg_path=args.kg_path,
            mode='train'
        )
        
        logger.info(f"Dataset loaded successfully with {len(test_dataset)} samples")
        
        # Test getting a sample
        sample = test_dataset[0]
        logger.info(f"Sample keys: {sample.keys()}")
        logger.info(f"Image shape: {sample['image'].shape}")
        logger.info(f"Labels shape: {sample['labels'].shape}")
        logger.info(f"KG features shape: {sample['kg_features'].shape}")
        
        if not args.skip_training:
            # Step 4: Quick training
            model_path, test_results = run_quick_training(args)
            
            if model_path:
                # Step 5: Quick evaluation
                eval_metrics = run_quick_evaluation(model_path, args)
                
                # Save test results
                results = {
                    'training_results': test_results,
                    'evaluation_metrics': eval_metrics,
                    'test_info': {
                        'num_samples': args.num_samples,
                        'batch_size': args.batch_size,
                        'device': args.device
                    }
                }
                
                results_file = os.path.join(args.output_dir, 'quick_test_results.json')
                with open(results_file, 'w') as f:
                    json.dump(results, f, indent=2)
                
                logger.info(f"Test results saved to: {results_file}")
        
        logger.info("="*60)
        logger.info("Quick test completed successfully!")
        logger.info("="*60)
        
    except Exception as e:
        logger.error(f"Quick test failed: {e}")
        raise

if __name__ == "__main__":
    main()
