#!/bin/bash

# Complete MIR-Flickr experiment script
# This script runs the full pipeline: data preparation, training, evaluation, and analysis

set -e  # Exit on any error

# Configuration
DATA_PATH="/home/<USER>/workplace/dwb/data/mirflickr"
KG_PATH="/home/<USER>/workplace/dwb/MMIMDB_KG_Disentangle/kg_data"
OUTPUT_DIR="./output_mirflickr_complete"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
LOG_DIR="./logs"

# Create log directory
mkdir -p ${LOG_DIR}

# Function to log messages
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a ${LOG_DIR}/mirflickr_complete_experiment_${TIMESTAMP}.log
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check dependencies
log "Checking dependencies..."
if ! command_exists python; then
    log "ERROR: Python not found. Please install Python."
    exit 1
fi

if ! python -c "import torch" 2>/dev/null; then
    log "ERROR: PyTorch not found. Please install PyTorch."
    exit 1
fi

log "Dependencies check passed."

# Print configuration
log "Starting MIR-Flickr Complete Experiment"
log "Configuration:"
log "  Data Path: ${DATA_PATH}"
log "  KG Path: ${KG_PATH}"
log "  Output Dir: ${OUTPUT_DIR}"
log "  Timestamp: ${TIMESTAMP}"

# Parse command line arguments
USE_MOCK_DATA=false
NUM_MOCK_SAMPLES=1000
SKIP_TRAINING=false
SKIP_VISUALIZATION=false
SKIP_COMPARISON=false
BATCH_SIZE=32
NUM_EPOCHS=30
LEARNING_RATE=1e-4
DEVICE="cuda"

while [[ $# -gt 0 ]]; do
    case $1 in
        --mock-data)
            USE_MOCK_DATA=true
            shift
            ;;
        --num-mock-samples)
            NUM_MOCK_SAMPLES="$2"
            shift 2
            ;;
        --skip-training)
            SKIP_TRAINING=true
            shift
            ;;
        --skip-visualization)
            SKIP_VISUALIZATION=true
            shift
            ;;
        --skip-comparison)
            SKIP_COMPARISON=true
            shift
            ;;
        --batch-size)
            BATCH_SIZE="$2"
            shift 2
            ;;
        --num-epochs)
            NUM_EPOCHS="$2"
            shift 2
            ;;
        --lr)
            LEARNING_RATE="$2"
            shift 2
            ;;
        --device)
            DEVICE="$2"
            shift 2
            ;;
        --help)
            echo "Usage: $0 [OPTIONS]"
            echo "Options:"
            echo "  --mock-data              Use mock data instead of downloading real dataset"
            echo "  --num-mock-samples N     Number of mock samples to create (default: 1000)"
            echo "  --skip-training          Skip training and use existing model"
            echo "  --skip-visualization     Skip visualization generation"
            echo "  --skip-comparison        Skip comparison with MM-IMDB"
            echo "  --batch-size N           Batch size for training (default: 32)"
            echo "  --num-epochs N           Number of training epochs (default: 30)"
            echo "  --lr RATE                Learning rate (default: 1e-4)"
            echo "  --device DEVICE          Device to use (default: cuda)"
            echo "  --help                   Show this help message"
            exit 0
            ;;
        *)
            log "Unknown option: $1"
            exit 1
            ;;
    esac
done

# Build command arguments
PYTHON_ARGS="--data_path ${DATA_PATH} --kg_path ${KG_PATH} --output_dir ${OUTPUT_DIR}"
PYTHON_ARGS="${PYTHON_ARGS} --batch_size ${BATCH_SIZE} --num_epochs ${NUM_EPOCHS}"
PYTHON_ARGS="${PYTHON_ARGS} --lr ${LEARNING_RATE} --device ${DEVICE}"

if [ "$USE_MOCK_DATA" = true ]; then
    PYTHON_ARGS="${PYTHON_ARGS} --mock_data --num_mock_samples ${NUM_MOCK_SAMPLES}"
    log "Using mock data with ${NUM_MOCK_SAMPLES} samples"
fi

if [ "$SKIP_TRAINING" = true ]; then
    PYTHON_ARGS="${PYTHON_ARGS} --skip_training"
    log "Skipping training"
fi

if [ "$SKIP_VISUALIZATION" = true ]; then
    PYTHON_ARGS="${PYTHON_ARGS} --skip_visualization"
    log "Skipping visualization"
fi

if [ "$SKIP_COMPARISON" = true ]; then
    PYTHON_ARGS="${PYTHON_ARGS} --skip_comparison"
    log "Skipping comparison with MM-IMDB"
fi

# Run the complete experiment
log "Starting complete experiment..."
log "Command: python run_mirflickr_complete_experiment.py ${PYTHON_ARGS}"

# Run in background and capture output
python run_mirflickr_complete_experiment.py ${PYTHON_ARGS} 2>&1 | tee -a ${LOG_DIR}/mirflickr_complete_experiment_${TIMESTAMP}.log

# Check if experiment completed successfully
if [ $? -eq 0 ]; then
    log "Experiment completed successfully!"
    
    # Find the experiment directory
    EXP_DIR=$(find ${OUTPUT_DIR} -name "mirflickr_complete_experiment_*" -type d | sort | tail -1)
    
    if [ -n "$EXP_DIR" ]; then
        log "Experiment results saved to: ${EXP_DIR}"
        
        # Check for key output files
        if [ -f "${EXP_DIR}/EXPERIMENT_REPORT.md" ]; then
            log "Final report available at: ${EXP_DIR}/EXPERIMENT_REPORT.md"
        fi
        
        if [ -d "${EXP_DIR}/visualizations" ]; then
            log "Visualizations available in: ${EXP_DIR}/visualizations"
        fi
        
        if [ -d "${EXP_DIR}/results" ]; then
            log "Results available in: ${EXP_DIR}/results"
        fi
        
        # Display key results if available
        RESULTS_FILE="${EXP_DIR}/results/comprehensive_results.json"
        if [ -f "$RESULTS_FILE" ]; then
            log "Key Results:"
            python -c "
import json
try:
    with open('${RESULTS_FILE}', 'r') as f:
        results = json.load(f)
    basic = results.get('basic_metrics', {})
    print(f'  F1 Score: {basic.get(\"f1\", 0):.4f}')
    print(f'  Precision: {basic.get(\"precision\", 0):.4f}')
    print(f'  Recall: {basic.get(\"recall\", 0):.4f}')
    print(f'  mAP: {basic.get(\"mAP\", 0):.4f}')
except Exception as e:
    print(f'  Could not parse results: {e}')
" | tee -a ${LOG_DIR}/mirflickr_complete_experiment_${TIMESTAMP}.log
        fi
        
        # Generate additional analysis if requested
        if [ "$SKIP_VISUALIZATION" = false ] && [ -f "$RESULTS_FILE" ]; then
            log "Generating additional visualizations..."
            python visualize_mirflickr_results.py \
                --results_path "$RESULTS_FILE" \
                --output_dir "${EXP_DIR}/additional_visualizations" \
                2>&1 | tee -a ${LOG_DIR}/mirflickr_complete_experiment_${TIMESTAMP}.log
        fi
        
    else
        log "WARNING: Could not find experiment directory"
    fi
    
else
    log "ERROR: Experiment failed!"
    exit 1
fi

# Summary
log "============================================"
log "MIR-Flickr Complete Experiment Summary"
log "============================================"
log "Start Time: ${TIMESTAMP}"
log "End Time: $(date +"%Y%m%d_%H%M%S")"
log "Data Path: ${DATA_PATH}"
log "Output Directory: ${OUTPUT_DIR}"
log "Log File: ${LOG_DIR}/mirflickr_complete_experiment_${TIMESTAMP}.log"

if [ -n "$EXP_DIR" ]; then
    log "Experiment Directory: ${EXP_DIR}"
    log ""
    log "Generated Files:"
    log "  - ${EXP_DIR}/EXPERIMENT_REPORT.md (Final report)"
    log "  - ${EXP_DIR}/results/ (All results and metrics)"
    log "  - ${EXP_DIR}/visualizations/ (Generated plots)"
    log "  - ${EXP_DIR}/models/ (Trained models)"
    log "  - ${EXP_DIR}/analysis/ (Detailed analysis)"
    log "  - ${EXP_DIR}/comparison/ (Comparison with MM-IMDB)"
fi

log "============================================"
log "Experiment completed successfully!"
log "Check the experiment report for detailed results and analysis."
